using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text;
using Liam.TcpServer.Extensions;
using Liam.TcpServer.Interfaces;

namespace TcpServerExample;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpServer 示例程序 ===\n");

        // 创建主机构建器
        var builder = Host.CreateDefaultBuilder(args);

        // 配置服务
        builder.ConfigureServices((context, services) =>
        {
            // 添加TCP服务器服务
            services.AddTcpServer(config =>
            {
                config.Port = 8888;
                config.MaxConnections = 100;
                config.EnableHeartbeat = true;
                config.HeartbeatIntervalSeconds = 30;
                config.HeartbeatTimeoutSeconds = 10;
            });

            // 启用日志记录
            services.AddTcpServerLogging();
        });

        // 构建主机
        var host = builder.Build();

        // 获取TCP服务器实例
        var tcpServer = host.Services.GetRequiredService<ITcpServer>();
        var logger = host.Services.GetRequiredService<ILogger<Program>>();

        // 订阅事件
        tcpServer.ClientConnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 客户端已连接: {e.Connection.ClientIpAddress}:{e.Connection.ClientPort}");
            Console.WriteLine($"    连接ID: {e.Connection.Id}");
            Console.WriteLine($"    连接时间: {e.ConnectedAt:yyyy-MM-dd HH:mm:ss}");
            
            // 发送欢迎消息
            _ = Task.Run(async () =>
            {
                await Task.Delay(1000); // 等待1秒
                await tcpServer.SendTextAsync(e.Connection.Id, "欢迎连接到Liam.TcpServer示例服务器！");
            });
        };

        tcpServer.ClientDisconnected += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 客户端已断开: {e.Connection.ClientIpAddress}:{e.Connection.ClientPort}");
            Console.WriteLine($"    连接ID: {e.Connection.Id}");
            Console.WriteLine($"    断开原因: {e.Reason ?? "未指定"}");
            Console.WriteLine($"    连接持续时间: {e.Connection.ConnectionDuration:hh\\:mm\\:ss}");
        };

        tcpServer.DataReceived += async (sender, e) =>
        {
            var message = Encoding.UTF8.GetString(e.Data);
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 收到数据 ({e.Data.Length} 字节): {message}");
            
            // 处理特殊命令
            if (message.StartsWith("/"))
            {
                await HandleCommand(tcpServer, e.Connection.Id, message);
            }
            else
            {
                // 回显消息
                var response = $"Echo: {message}";
                await tcpServer.SendTextAsync(e.Connection.Id, response);
            }
        };

        tcpServer.Error += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 发生错误: {e.Exception.Message}");
            if (e.Connection != null)
            {
                Console.WriteLine($"    相关连接: {e.Connection.Id}");
            }
        };

        tcpServer.Heartbeat += (sender, e) =>
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 心跳{(e.IsRequest ? "请求" : "响应")}: {e.Connection.Id}");
        };

        try
        {
            // 启动服务器
            Console.WriteLine("正在启动TCP服务器...");
            await tcpServer.StartAsync();
            
            Console.WriteLine($"TCP服务器已启动，监听端口: {tcpServer.Configuration.Port}");
            Console.WriteLine("可用命令:");
            Console.WriteLine("  /help     - 显示帮助信息");
            Console.WriteLine("  /stats    - 显示服务器统计信息");
            Console.WriteLine("  /clients  - 显示连接的客户端");
            Console.WriteLine("  /broadcast <message> - 广播消息到所有客户端");
            Console.WriteLine("  /quit     - 停止服务器");
            Console.WriteLine("\n等待客户端连接...");
            Console.WriteLine("提示: 可以使用telnet命令连接测试: telnet localhost 8080\n");

            // 处理控制台输入
            await HandleConsoleInput(tcpServer);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"启动服务器时发生错误: {ex.Message}");
        }
        finally
        {
            // 停止服务器
            Console.WriteLine("\n正在停止TCP服务器...");
            await tcpServer.StopAsync();
            Console.WriteLine("TCP服务器已停止");
        }
    }

    /// <summary>
    /// 处理客户端命令
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <param name="connectionId">连接ID</param>
    /// <param name="command">命令</param>
    /// <returns>处理任务</returns>
    static async Task HandleCommand(ITcpServer server, string connectionId, string command)
    {
        var parts = command.Split(' ', StringSplitOptions.RemoveEmptyEntries);
        var cmd = parts[0].ToLower();

        switch (cmd)
        {
            case "/help":
                var helpText = """
                可用命令:
                /help - 显示此帮助信息
                /time - 获取服务器时间
                /stats - 获取连接统计信息
                /echo <message> - 回显消息
                /quit - 断开连接
                """;
                await server.SendTextAsync(connectionId, helpText);
                break;

            case "/time":
                var timeText = $"服务器时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                await server.SendTextAsync(connectionId, timeText);
                break;

            case "/stats":
                var connection = server.GetConnection(connectionId);
                if (connection != null)
                {
                    var statsText = $"""
                    连接统计信息:
                    连接ID: {connection.Id}
                    客户端地址: {connection.ClientIpAddress}:{connection.ClientPort}
                    连接时间: {connection.ConnectedAt:yyyy-MM-dd HH:mm:ss}
                    连接持续时间: {connection.ConnectionDuration:hh\:mm\:ss}
                    发送字节数: {connection.Statistics.BytesSent}
                    接收字节数: {connection.Statistics.BytesReceived}
                    发送消息数: {connection.Statistics.MessagesSent}
                    接收消息数: {connection.Statistics.MessagesReceived}
                    """;
                    await server.SendTextAsync(connectionId, statsText);
                }
                break;

            case "/echo":
                if (parts.Length > 1)
                {
                    var message = string.Join(" ", parts.Skip(1));
                    await server.SendTextAsync(connectionId, $"Echo: {message}");
                }
                else
                {
                    await server.SendTextAsync(connectionId, "用法: /echo <message>");
                }
                break;

            case "/quit":
                await server.SendTextAsync(connectionId, "再见！");
                await server.DisconnectClientAsync(connectionId, "客户端请求断开");
                break;

            default:
                await server.SendTextAsync(connectionId, $"未知命令: {cmd}，输入 /help 查看可用命令");
                break;
        }
    }

    /// <summary>
    /// 处理控制台输入
    /// </summary>
    /// <param name="server">TCP服务器</param>
    /// <returns>处理任务</returns>
    static async Task HandleConsoleInput(ITcpServer server)
    {
        while (server.IsRunning)
        {
            var input = Console.ReadLine();
            if (string.IsNullOrEmpty(input))
                continue;

            var parts = input.Split(' ', StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length == 0)
                continue;

            var command = parts[0].ToLower();

            switch (command)
            {
                case "/help":
                    Console.WriteLine("服务器控制台命令:");
                    Console.WriteLine("  /help     - 显示此帮助信息");
                    Console.WriteLine("  /stats    - 显示服务器统计信息");
                    Console.WriteLine("  /clients  - 显示连接的客户端");
                    Console.WriteLine("  /broadcast <message> - 广播消息到所有客户端");
                    Console.WriteLine("  /quit     - 停止服务器");
                    break;

                case "/stats":
                    var stats = server.GetStatistics();
                    Console.WriteLine($"\n=== 服务器统计信息 ===");
                    Console.WriteLine($"当前连接数: {stats.CurrentConnections}");
                    Console.WriteLine($"总连接数: {stats.TotalConnections}");
                    Console.WriteLine($"最大并发连接数: {stats.MaxConcurrentConnections}");
                    Console.WriteLine($"总发送字节数: {stats.TotalBytesSent:N0}");
                    Console.WriteLine($"总接收字节数: {stats.TotalBytesReceived:N0}");
                    Console.WriteLine($"总发送消息数: {stats.TotalMessagesSent:N0}");
                    Console.WriteLine($"总接收消息数: {stats.TotalMessagesReceived:N0}");
                    Console.WriteLine($"总错误数: {stats.TotalErrors:N0}");
                    Console.WriteLine($"运行时间: {stats.Uptime?.ToString(@"dd\.hh\:mm\:ss") ?? "未知"}");
                    Console.WriteLine($"错误率: {stats.ErrorRate:P2}");
                    Console.WriteLine();
                    break;

                case "/clients":
                    var connections = server.GetActiveConnections();
                    Console.WriteLine($"\n=== 活跃连接 ({connections.Count}) ===");
                    if (connections.Count == 0)
                    {
                        Console.WriteLine("无活跃连接");
                    }
                    else
                    {
                        foreach (var conn in connections)
                        {
                            Console.WriteLine($"  {conn.GetConnectionSummary()}");
                        }
                    }
                    Console.WriteLine();
                    break;

                case "/broadcast":
                    if (parts.Length > 1)
                    {
                        var message = string.Join(" ", parts.Skip(1));
                        var count = await server.BroadcastTextAsync($"[服务器广播] {message}");
                        Console.WriteLine($"消息已广播到 {count} 个客户端");
                    }
                    else
                    {
                        Console.WriteLine("用法: /broadcast <message>");
                    }
                    break;

                case "/quit":
                    Console.WriteLine("正在停止服务器...");
                    return;

                default:
                    Console.WriteLine($"未知命令: {command}，输入 /help 查看可用命令");
                    break;
            }
        }
    }
}
