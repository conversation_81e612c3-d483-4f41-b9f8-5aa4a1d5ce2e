using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Liam.TcpServer.Interfaces;
using Liam.TcpServer.Models;
using Liam.TcpServer.Services;
using Liam.TcpServer.Handlers;

namespace Liam.TcpServer.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加TCP服务器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpServer(this IServiceCollection services, Action<TcpServerConfig>? configureOptions = null)
    {
        // 注册配置
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }
        else
        {
            services.Configure<TcpServerConfig>(config => { });
        }

        // 注册核心服务
        services.TryAddSingleton<IConnectionManager, ConnectionManager>();
        services.TryAddSingleton<ISecurityManager, SecurityManager>();
        services.TryAddSingleton<IMessageHandler, MessageHandler>();
        services.TryAddSingleton<IMessageHandlerRegistry, MessageHandlerRegistry>();
        
        // 注册TCP服务器
        services.TryAddSingleton<ITcpServer, Services.TcpServer>();

        // 注册处理器
        services.TryAddTransient<HeartbeatHandler>();

        return services;
    }

    /// <summary>
    /// 添加TCP服务器服务（使用配置实例）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="config">配置实例</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpServer(this IServiceCollection services, TcpServerConfig config)
    {
        ArgumentNullException.ThrowIfNull(config);

        services.AddSingleton(Options.Create(config));
        return services.AddTcpServer();
    }

    /// <summary>
    /// 添加TCP服务器服务（使用默认配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="port">端口号</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpServer(this IServiceCollection services, int port)
    {
        return services.AddTcpServer(config => config.Port = port);
    }

    /// <summary>
    /// 添加SSL TCP服务器服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="certificate">SSL证书</param>
    /// <param name="port">端口号</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSslTcpServer(this IServiceCollection services, System.Security.Cryptography.X509Certificates.X509Certificate2 certificate, int port = 8443)
    {
        ArgumentNullException.ThrowIfNull(certificate);

        return services.AddTcpServer(config =>
        {
            config.Port = port;
            config.EnableSsl = true;
            config.SslCertificate = certificate;
        });
    }

    /// <summary>
    /// 添加自定义消息处理器
    /// </summary>
    /// <typeparam name="T">消息类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <param name="handler">消息处理器</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCustomMessageHandler<T>(this IServiceCollection services, ICustomMessageHandler<T> handler) where T : class
    {
        ArgumentNullException.ThrowIfNull(handler);

        services.AddSingleton(handler);
        
        // 注册到处理器注册表
        services.Configure<MessageHandlerRegistryOptions>(options =>
        {
            options.Handlers.Add(handler.MessageType, handler);
        });

        return services;
    }

    /// <summary>
    /// 添加自定义消息处理器
    /// </summary>
    /// <typeparam name="THandler">处理器类型</typeparam>
    /// <typeparam name="TMessage">消息类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddCustomMessageHandler<THandler, TMessage>(this IServiceCollection services) 
        where THandler : class, ICustomMessageHandler<TMessage> 
        where TMessage : class
    {
        services.AddSingleton<ICustomMessageHandler<TMessage>, THandler>();
        return services;
    }

    /// <summary>
    /// 配置TCP服务器安全设置
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureOptions">配置选项</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureTcpServerSecurity(this IServiceCollection services, Action<SecuritySettings> configureOptions)
    {
        ArgumentNullException.ThrowIfNull(configureOptions);

        services.Configure<TcpServerConfig>(config =>
        {
            configureOptions(config.Security);
        });

        return services;
    }

    /// <summary>
    /// 启用TCP服务器白名单
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="allowedIps">允许的IP地址</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnableTcpServerWhitelist(this IServiceCollection services, params string[] allowedIps)
    {
        return services.ConfigureTcpServerSecurity(security =>
        {
            security.EnableWhitelist = true;
            foreach (var ip in allowedIps)
            {
                security.AddToWhitelist(ip);
            }
        });
    }

    /// <summary>
    /// 启用TCP服务器黑名单
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="blockedIps">阻止的IP地址</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnableTcpServerBlacklist(this IServiceCollection services, params string[] blockedIps)
    {
        return services.ConfigureTcpServerSecurity(security =>
        {
            security.EnableBlacklist = true;
            foreach (var ip in blockedIps)
            {
                security.AddToBlacklist(ip);
            }
        });
    }

    /// <summary>
    /// 启用TCP服务器连接频率限制
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="maxConnectionsPerMinute">每分钟最大连接数</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnableTcpServerRateLimit(this IServiceCollection services, int maxConnectionsPerMinute = 60)
    {
        return services.ConfigureTcpServerSecurity(security =>
        {
            security.EnableConnectionRateLimit = true;
            security.ConnectionRateLimit = maxConnectionsPerMinute;
        });
    }

    /// <summary>
    /// 启用TCP服务器认证
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="authenticationKey">认证密钥</param>
    /// <param name="timeoutSeconds">认证超时时间（秒）</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnableTcpServerAuthentication(this IServiceCollection services, string authenticationKey, int timeoutSeconds = 30)
    {
        ArgumentNullException.ThrowIfNull(authenticationKey);

        return services.ConfigureTcpServerSecurity(security =>
        {
            security.EnableAuthentication = true;
            security.AuthenticationKey = authenticationKey;
            security.AuthenticationTimeoutSeconds = timeoutSeconds;
        });
    }

    /// <summary>
    /// 启用TCP服务器心跳检测
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="intervalSeconds">心跳间隔（秒）</param>
    /// <param name="timeoutSeconds">心跳超时时间（秒）</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection EnableTcpServerHeartbeat(this IServiceCollection services, int intervalSeconds = 60, int timeoutSeconds = 10)
    {
        return services.Configure<TcpServerConfig>(config =>
        {
            config.EnableHeartbeat = true;
            config.HeartbeatIntervalSeconds = intervalSeconds;
            config.HeartbeatTimeoutSeconds = timeoutSeconds;
        });
    }

    /// <summary>
    /// 配置TCP服务器性能监控
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="enabled">是否启用</param>
    /// <param name="updateIntervalSeconds">更新间隔（秒）</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureTcpServerPerformanceMonitoring(this IServiceCollection services, bool enabled = true, int updateIntervalSeconds = 10)
    {
        return services.Configure<TcpServerConfig>(config =>
        {
            config.EnablePerformanceMonitoring = enabled;
            config.StatisticsUpdateIntervalSeconds = updateIntervalSeconds;
        });
    }

    /// <summary>
    /// 添加Liam.Logging集成
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddTcpServerLogging(this IServiceCollection services)
    {
        // 确保已添加Liam.Logging服务
        services.TryAddSingleton<Liam.Logging.Interfaces.ILiamLogger, Liam.Logging.Services.LiamLogger>();
        
        return services.Configure<TcpServerConfig>(config =>
        {
            config.EnableLogging = true;
        });
    }
}

/// <summary>
/// 消息处理器注册表选项
/// </summary>
public class MessageHandlerRegistryOptions
{
    /// <summary>
    /// 处理器字典
    /// </summary>
    public Dictionary<byte, object> Handlers { get; } = new();
}
