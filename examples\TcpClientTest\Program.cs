using System.Net.Sockets;
using System.Text;

namespace TcpClientTest;

class Program
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Liam.TcpServer 客户端测试工具 ===\n");

        string serverHost = "localhost";
        int serverPort = 8888;

        // 解析命令行参数
        if (args.Length >= 1)
        {
            serverHost = args[0];
        }
        if (args.Length >= 2 && int.TryParse(args[1], out int port))
        {
            serverPort = port;
        }

        Console.WriteLine($"连接到服务器: {serverHost}:{serverPort}");
        Console.WriteLine("输入消息发送到服务器，输入 'quit' 退出\n");

        try
        {
            using var client = new TcpClient();
            await client.ConnectAsync(serverHost, serverPort);
            
            Console.WriteLine("已连接到服务器！");
            Console.WriteLine("可用命令:");
            Console.WriteLine("  /help     - 显示服务器帮助信息");
            Console.WriteLine("  /time     - 获取服务器时间");
            Console.WriteLine("  /stats    - 获取连接统计信息");
            Console.WriteLine("  /echo <message> - 回显消息");
            Console.WriteLine("  /quit     - 断开连接");
            Console.WriteLine("  quit      - 退出客户端");
            Console.WriteLine();

            using var stream = client.GetStream();
            
            // 启动接收任务
            var receiveTask = ReceiveMessagesAsync(stream);
            
            // 发送消息循环
            while (client.Connected)
            {
                Console.Write("> ");
                var input = Console.ReadLine();
                
                if (string.IsNullOrEmpty(input))
                    continue;
                
                if (input.ToLower() == "quit")
                {
                    Console.WriteLine("正在断开连接...");
                    break;
                }
                
                try
                {
                    var data = Encoding.UTF8.GetBytes(input);
                    await stream.WriteAsync(data);
                    await stream.FlushAsync();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发送消息失败: {ex.Message}");
                    break;
                }
            }
            
            // 等待接收任务完成
            try
            {
                await receiveTask.WaitAsync(TimeSpan.FromSeconds(5));
            }
            catch (TimeoutException)
            {
                Console.WriteLine("等待接收任务完成超时");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"连接失败: {ex.Message}");
        }
        
        Console.WriteLine("客户端已退出");
    }

    /// <summary>
    /// 接收服务器消息
    /// </summary>
    /// <param name="stream">网络流</param>
    /// <returns>接收任务</returns>
    static async Task ReceiveMessagesAsync(NetworkStream stream)
    {
        var buffer = new byte[4096];
        
        try
        {
            while (stream.CanRead)
            {
                var bytesRead = await stream.ReadAsync(buffer);
                if (bytesRead == 0)
                {
                    Console.WriteLine("\n服务器已断开连接");
                    break;
                }
                
                var message = Encoding.UTF8.GetString(buffer, 0, bytesRead);
                Console.WriteLine($"\n[服务器] {message}");
                Console.Write("> ");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n接收消息时发生错误: {ex.Message}");
        }
    }
}
